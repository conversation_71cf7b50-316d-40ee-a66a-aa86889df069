# SCDF Application Makefile

.PHONY: help build up down logs clean dev prod health setup

# Default target
help:
	@echo "SCDF Application Deployment Commands"
	@echo "===================================="
	@echo "setup     - Set up environment files from examples"
	@echo "dev       - Start development environment"
	@echo "prod      - Start production environment"
	@echo "build     - Build all Docker images"
	@echo "up        - Start services (default configuration)"
	@echo "down      - Stop all services"
	@echo "logs      - View logs from all services"
	@echo "health    - Check health of all services"
	@echo "clean     - Clean up Docker resources"

# Set up environment files
setup:
	@echo "Setting up environment files..."
	@if [ ! -f backend/.env ]; then cp backend/.env.example backend/.env; echo "Created backend/.env"; fi
	@if [ ! -f frontend/.env ]; then cp frontend/.env.example frontend/.env; echo "Created frontend/.env"; fi
	@echo "Please edit the .env files with your actual values before deployment"

# Development environment
dev:
	@echo "Starting development environment..."
	docker-compose -f docker-compose.dev.yml up -d --build

# Production environment
prod:
	@echo "Starting production environment..."
	docker compose -f docker-compose.prod.yml up -d --build

# Build all images
build:
	@echo "Building all Docker images..."
	docker-compose build --no-cache

# Start services (default)
up:
	@echo "Starting services..."
	docker-compose up -d --build

# Stop all services
down:
	@echo "Stopping all services..."
	docker-compose down
	docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
	docker-compose -f docker-compose.prod.yml down 2>/dev/null || true

# View logs
logs:
	@echo "Viewing logs..."
	docker-compose logs -f

# Health check
health:
	@echo "Checking service health..."
	@echo "Backend health:"
	@curl -s http://localhost:8000/health | jq . || echo "Backend not responding"
	@echo "Frontend health:"
	@curl -s http://localhost:3000/api/health | jq . || echo "Frontend not responding"

# Clean up Docker resources
clean:
	@echo "Cleaning up Docker resources..."
	docker-compose down -v --remove-orphans
	docker-compose -f docker-compose.dev.yml down -v --remove-orphans 2>/dev/null || true
	docker-compose -f docker-compose.prod.yml down -v --remove-orphans 2>/dev/null || true
	docker system prune -f
