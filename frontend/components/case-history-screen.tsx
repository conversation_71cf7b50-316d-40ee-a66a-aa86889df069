"use client"

import React from 'react'
import { useAtom } from 'jotai'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Loader2 } from "lucide-react"
import { useCases } from "@/hooks/use-cases"
import { useInterviews } from "@/hooks/use-interviews"
import { selectedCaseAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"
import type { Case } from "@/types/database"

interface CaseHistoryScreenProps {
  onNavigate: (screen: Screen) => void
}

export function CaseHistoryScreen({ onNavigate }: CaseHistoryScreenProps) {
  const { cases, loading, error, refetch } = useCases();
  const [, setSelectedCase] = useAtom(selectedCaseAtom)

  // Force refetch when component mounts to ensure fresh data
  React.useEffect(() => {
    refetch();
  }, [refetch]);

  const handleSelectCase = (selectedCase: Case) => {
    setSelectedCase(selectedCase)
    onNavigate("case-details")
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (timeString: string) => {
    const [hours, minutes] = timeString.split(":")
    const hour = Number.parseInt(hours)
    const ampm = hour >= 12 ? "PM" : "AM"
    const displayHour = hour % 12 || 12
    return `${displayHour}:${minutes} ${ampm}`
  }

  const getCaseTitle = (caseData: Case) => {
    return `Incident at ${caseData.incidentLocation} on ${formatDate(caseData.incidentDate)}`;
  };
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("start-screen")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Case History</h2>
        <div></div>
      </div>

      <div className="space-y-4">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin mr-2" />
            <span>Loading case history...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Error loading cases: {error}</p>
            <Button variant="outline" onClick={refetch}>
              Try Again
            </Button>
          </div>
        ) : cases.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">No cases found</p>
            <Button variant="outline" onClick={refetch}>
              Refresh
            </Button>
          </div>
        ) : (
          cases.map((caseItem) => (
            <Card key={caseItem.id} onClick={() => handleSelectCase(caseItem)}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-semibold text-primary">{getCaseTitle(caseItem)}</h4>
                  <Badge variant={caseItem.status === "In Progress" ? "secondary" : "default"}>{caseItem.status}</Badge>
                </div>
                <div className="space-y-1 text-sm">
                  <p>
                    <strong>Location:</strong> {caseItem.incidentLocation}
                  </p>
                  <p>
                    <strong>Date:</strong> {formatDate(caseItem.incidentDate)} at {formatTime(caseItem.incidentTime)}
                  </p>
                  <p>
                    <strong>Officer:</strong> {caseItem.assignedOfficer?.fullName || 'Unknown'}
                  </p>
                  <p>
                    <strong>Created:</strong> {formatDate(caseItem.createdAt.toISOString().split('T')[0])}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
