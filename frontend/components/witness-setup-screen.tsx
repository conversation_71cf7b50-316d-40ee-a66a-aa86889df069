"use client"

import type React from "react"

import { useState } from "react"
import { useAtom } from 'jotai'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowLeft, Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { currentWitnessAtom, currentCaseAtom, saveRecordingAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"
import type { <PERSON>, InterviewEnvironment } from "@/types/database"

const witnessTypeOptions = [
  "Resident",
  "Neighbor",
  "Passerby",
  "Business Owner",
  "Emergency Responder",
  "Property Owner",
  "Tenant",
  "Visitor",
  "Employee",
  "Security Personnel"
]

interface WitnessSetupScreenProps {
  onNavigate: (screen: Screen) => void
}

export function WitnessSetupScreen({ onNavigate }: WitnessSetupScreenProps) {
  const { toast } = useToast()
  const [, setCurrentWitness] = useAtom(currentWitnessAtom)
  const [currentCase] = useAtom(currentCaseAtom)
  const [saveRecording, setSaveRecording] = useAtom(saveRecordingAtom)
  const [formData, setFormData] = useState({
    witnessName: "",
    witnessType: "",
    witnessContact: "",
    interviewEnvironment: "",
  })
  const [witnessTypeOpen, setWitnessTypeOpen] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!formData.witnessContact || !formData.witnessType || !formData.interviewEnvironment || !formData.witnessName) {
      toast({
        title: "Missing Information",
        description: "Please provide all information about the witness.",
        variant: "destructive",
      })
      return
    }

    const witness: Witness = {
      name: formData.witnessName,
      type: formData.witnessType as string,
      contact: formData.witnessContact,
      environment: formData.interviewEnvironment as InterviewEnvironment,
    }

    setCurrentWitness(witness)
    onNavigate("recording-screen")
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-selection")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">Witness Interview Setup</h2>
        <div></div>
      </div>

      {currentCase && (
        <Card className="mb-6">
          <CardContent className="p-4 text-center">
            <h4 className="font-semibold text-primary mb-2">{currentCase.id}</h4>
            <p className="text-muted-foreground">{currentCase.incidentLocation}</p>
          </CardContent>
        </Card>
      )}

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="witness-name">Witness Name</Label>
              <Input
                id="witness-name"
                value={formData.witnessName}
                onChange={(e) => handleInputChange("witnessName", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-type">Witness Type</Label>
              <Popover open={witnessTypeOpen} onOpenChange={setWitnessTypeOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={witnessTypeOpen}
                    className="w-full justify-between"
                  >
                    {formData.witnessType || "Select or type witness type..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput
                      placeholder="Search or type witness type..."
                      value={formData.witnessType}
                      onValueChange={(value) => handleInputChange("witnessType", value)}
                    />
                    <CommandList>
                      <CommandEmpty>
                        <div className="p-2">
                          <Button
                            variant="ghost"
                            className="w-full justify-start"
                            onClick={() => {
                              setWitnessTypeOpen(false)
                            }}
                          >
                            <Check className="mr-2 h-4 w-4" />
                            Use "{formData.witnessType}"
                          </Button>
                        </div>
                      </CommandEmpty>
                      <CommandGroup>
                        {witnessTypeOptions.map((option) => (
                          <CommandItem
                            key={option}
                            value={option}
                            onSelect={(currentValue) => {
                              handleInputChange("witnessType", currentValue)
                              setWitnessTypeOpen(false)
                            }}
                          >
                            <Check
                              className={cn(
                                "mr-2 h-4 w-4",
                                formData.witnessType === option ? "opacity-100" : "opacity-0"
                              )}
                            />
                            {option}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="witness-contact">Contact Information</Label>
              <Input
                id="witness-contact"
                value={formData.witnessContact}
                onChange={(e) => handleInputChange("witnessContact", e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="interview-environment">Interview Environment</Label>
              <Select
                value={formData.interviewEnvironment}
                onValueChange={(value) => handleInputChange("interviewEnvironment", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select environment..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="controlled">Controlled Environment (Station/Office)</SelectItem>
                  <SelectItem value="field">Field Environment (On-Scene)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2 p-4 bg-gray-50 rounded-lg">
              <Checkbox
                id="save-recording"
                checked={saveRecording}
                onCheckedChange={(checked) => setSaveRecording(checked as boolean)}
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor="save-recording"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  Save audio recording
                </Label>
                <p className="text-xs text-muted-foreground">
                  Keep a copy of the interview audio file for future reference
                </p>
              </div>
            </div>

            <Button type="submit" className="w-full">
              Start Interview
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
