"use client"

import { useState } from "react"
import { useAtom } from 'jotai'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { FileText, Download, Loader2, CheckCircle, AlertCircle, PenTool } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { generateInterviewPDF, downloadPDF, generateStatementFormPDF } from "@/lib/pdf-export"
import { generateInterviewDOCX, downloadDOCX, generateStatementFormDOCX } from "@/lib/docx-export"
import { exportDataAtom, resetAllDataAtom } from "@/store/atoms"
import type { Screen } from "@/app/page"

interface ExportScreenProps {
  onNavigate: (screen: Screen) => void
}

export function ExportScreen({ onNavigate }: ExportScreenProps) {
  const { toast } = useToast()
  const [isExportingPDF, setIsExportingPDF] = useState(false)
  const [isExportingDOCX, setIsExportingDOCX] = useState(false)
  const [isExportingStatementPDF, setIsExportingStatementPDF] = useState(false)
  const [isExportingStatementDOCX, setIsExportingStatementDOCX] = useState(false)
  const [exportComplete, setExportComplete] = useState(false)
  const [exportData] = useAtom(exportDataAtom)
  const [, resetAllData] = useAtom(resetAllDataAtom)

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const handleExportPDF = async () => {
    if (!exportData) {
      toast({
        title: "Export Error",
        description: "No interview data available for export. Please complete an interview first.",
        variant: "destructive",
      })
      return
    }

    setIsExportingPDF(true)
    try {
      // Generate PDF
      const pdfBlob = await generateInterviewPDF(
        exportData.interview!,
        exportData.case!,
        exportData.officer!,
        exportData.transcriptData || undefined,
        exportData.statement || undefined
      )

      // Create filename
      const filename = `Interview_${exportData.interview!.id.slice(-8)}_${exportData.interview!.witness.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`

      // Download and open PDF
      downloadPDF(pdfBlob, filename)

      setExportComplete(true)
      toast({
        title: "Export Successful",
        description: "Interview PDF has been generated and downloaded. A new tab has been opened to view the file.",
      })

    } catch (error) {
      console.error('Error exporting PDF:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export interview PDF",
        variant: "destructive",
      })
    } finally {
      setIsExportingPDF(false)
    }
  }



  if (!exportData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold">Export Interview</h2>
        </div>

        <Alert variant="destructive" className="max-w-md mx-auto mb-8">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            No interview data available for export. Please complete an interview first.
          </AlertDescription>
        </Alert>

        <div className="flex gap-3 max-w-sm mx-auto">
          <Button
            variant="outline"
            className="flex-1"
            onClick={() => onNavigate("start-screen")}
          >
            Start New Interview
          </Button>
          <Button variant="secondary" className="flex-1" onClick={() => onNavigate("case-history")}>
            View Case History
          </Button>
        </div>
      </div>
    )
  }

  const handleExportDOCX = async () => {
    if (!exportData) {
      toast({
        title: "Export Error",
        description: "No interview data available for export. Please complete an interview first.",
        variant: "destructive",
      })
      return
    }

    setIsExportingDOCX(true)
    try {
      // Generate DOCX
      const docxBlob = await generateInterviewDOCX({
        interview: exportData.interview!,
        case: exportData.case!,
        officer: exportData.officer!,
        transcriptData: exportData.transcriptData || undefined,
        statement: exportData.statement || undefined,
      })

      // Create filename
      const filename = `Interview_${exportData.interview!.id.slice(-8)}_${exportData.interview!.witness.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.docx`

      // Download DOCX
      downloadDOCX(docxBlob, filename)

      setExportComplete(true)
      toast({
        title: "Export Successful",
        description: "Interview DOCX has been generated and downloaded.",
      })

    } catch (error) {
      console.error('Error exporting DOCX:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export interview DOCX",
        variant: "destructive",
      })
    } finally {
      setIsExportingDOCX(false)
    }
  }

  const handleExportStatementPDF = async () => {
    if (!exportData || !exportData.statement?.statementForm) {
      toast({
        title: "Export Error",
        description: "No statement form data available for export. Please complete a statement form first.",
        variant: "destructive",
      })
      return
    }

    setIsExportingStatementPDF(true)
    try {
      // Generate Statement Form PDF
      const pdfBlob = await generateStatementFormPDF(exportData.statement.statementForm)

      // Create filename
      const filename = `Statement_Form_${exportData.interview!.id.slice(-8)}_${exportData.interview!.witness.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`

      // Download and open PDF
      downloadPDF(pdfBlob, filename)

      toast({
        title: "Export Successful",
        description: "Statement form PDF has been generated and downloaded. A new tab has been opened to view the file.",
      })

    } catch (error) {
      console.error('Error exporting statement PDF:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export statement form PDF",
        variant: "destructive",
      })
    } finally {
      setIsExportingStatementPDF(false)
    }
  }

  const handleExportStatementDOCX = async () => {
    if (!exportData || !exportData.statement?.statementForm) {
      toast({
        title: "Export Error",
        description: "No statement form data available for export. Please complete a statement form first.",
        variant: "destructive",
      })
      return
    }

    setIsExportingStatementDOCX(true)
    try {
      // Generate Statement Form DOCX
      const docxBlob = await generateStatementFormDOCX(exportData.statement.statementForm)

      // Create filename
      const filename = `Statement_Form_${exportData.interview!.id.slice(-8)}_${exportData.interview!.witness.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.docx`

      // Download DOCX
      downloadDOCX(docxBlob, filename)

      toast({
        title: "Export Successful",
        description: "Statement form DOCX has been generated and downloaded.",
      })

    } catch (error) {
      console.error('Error exporting statement DOCX:', error)
      toast({
        title: "Export Failed",
        description: error instanceof Error ? error.message : "Failed to export statement form DOCX",
        variant: "destructive",
      })
    } finally {
      setIsExportingStatementDOCX(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-semibold">Export Interview</h2>
      </div>

      {/* Export Status */}
      {exportComplete && (
        <Alert className="max-w-md mx-auto mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            Interview has been successfully exported as PDF!
          </AlertDescription>
        </Alert>
      )}

      {/* Interview Summary */}
      <Card className="max-w-2xl mx-auto mb-8">
        <CardHeader>
          <CardTitle>Interview Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Case Information</h4>
              <div className="text-sm space-y-1">
                <p><strong>Case ID:</strong> {exportData.case?.id}</p>
                <p><strong>Location:</strong> {exportData.case?.incidentLocation}</p>
                <p><strong>Date:</strong> {exportData.case?.incidentDate} at {exportData.case?.incidentTime}</p>
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-700">Interview Details</h4>
              <div className="text-sm space-y-1">
                <p><strong>Interview ID:</strong> {exportData.interview?.id.slice(-8)}</p>
                <p><strong>Officer:</strong> {exportData.officer?.fullName}</p>
                <p><strong>Witness:</strong> {exportData.interview?.witness.name}</p>
                {exportData.interview?.duration && (
                  <p><strong>Duration:</strong> {formatDuration(exportData.interview.duration)}</p>
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 pt-4 border-t">
            <h4 className="font-semibold text-sm text-gray-700 mb-2">Export Contents</h4>
            <div className="flex flex-wrap gap-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                <CheckCircle className="w-3 h-3 mr-1" />
                Interview Details
              </span>
              {exportData.transcriptData && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Transcription
                </span>
              )}
              {exportData.statement && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-100 text-purple-800">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Statement
                </span>
              )}
              {exportData.statement?.statementForm && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-indigo-100 text-indigo-800">
                  <FileText className="w-3 h-3 mr-1" />
                  Statement Form
                </span>
              )}
              {exportData.interview?.witnessSignaturePath && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-orange-100 text-orange-800">
                  <PenTool className="w-3 h-3 mr-1" />
                  Witness Signature
                </span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      <div className="max-w-lg mx-auto mb-8 space-y-6">
        {/* Interview Export */}
        <div>
          <h4 className="font-semibold text-center mb-4">Interview Export</h4>
          <div className="space-y-3">
            <Button
              className="w-full flex items-center gap-3"
              size="lg"
              onClick={handleExportPDF}
              disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
            >
              {isExportingPDF ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Generating PDF...
                </>
              ) : (
                <>
                  <FileText className="w-5 h-5" />
                  Export Interview as PDF
                </>
              )}
            </Button>
            <Button
              variant="secondary"
              className="w-full flex items-center gap-3"
              size="lg"
              onClick={handleExportDOCX}
              disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
            >
              {isExportingDOCX ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  Generating DOCX...
                </>
              ) : (
                <>
                  <Download className="w-5 h-5" />
                  Export Interview as Word
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Statement Form Export */}
        {exportData.statement?.statementForm && (
          <div>
            <h4 className="font-semibold text-center mb-4">Statement Form Export</h4>
            <div className="space-y-3">
              <Button
                variant="outline"
                className="w-full flex items-center gap-3"
                size="lg"
                onClick={handleExportStatementPDF}
                disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
              >
                {isExportingStatementPDF ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Generating Statement PDF...
                  </>
                ) : (
                  <>
                    <FileText className="w-5 h-5" />
                    Export Statement Form as PDF
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                className="w-full flex items-center gap-3"
                size="lg"
                onClick={handleExportStatementDOCX}
                disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
              >
                {isExportingStatementDOCX ? (
                  <>
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Generating Statement DOCX...
                  </>
                ) : (
                  <>
                    <Download className="w-5 h-5" />
                    Export Statement Form as Word
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Navigation */}
      <div className="flex gap-3 max-w-sm mx-auto">
        <Button
          variant="outline"
          className="flex-1"
          onClick={() => {
            resetAllData()
            onNavigate("start-screen")
          }}
          disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
        >
          Start New Interview
        </Button>
        <Button
          variant="secondary"
          className="flex-1"
          onClick={() => onNavigate("case-history")}
          disabled={isExportingPDF || isExportingDOCX || isExportingStatementPDF || isExportingStatementDOCX}
        >
          View Case History
        </Button>
      </div>
    </div>
  )
}
