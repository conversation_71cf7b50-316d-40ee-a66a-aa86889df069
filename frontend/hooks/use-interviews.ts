"use client"

import { useState, useEffect } from 'react';
import { InterviewService } from '@/lib/supabase';
import type { Interview, InterviewFilters, CreateInterviewRequest, UpdateInterviewRequest } from '@/types/database';

interface UseInterviewsResult {
  interviews: Interview[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createInterview: (caseId: string, interviewData: CreateInterviewRequest) => Promise<Interview>;
}

export function useInterviews(filters?: InterviewFilters): UseInterviewsResult {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInterviews = async () => {
    try {
      setLoading(true);
      setError(null);

      const interviews = await InterviewService.getInterviews(filters);
      setInterviews(interviews);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setInterviews([]);
    } finally {
      setLoading(false);
    }
  };

  const createInterview = async (caseId: string, interviewData: CreateInterviewRequest): Promise<Interview> => {
    try {
      setError(null);

      const newInterview = await InterviewService.createInterview(caseId, interviewData);

      // Add the new interview to the local state
      setInterviews(prev => [newInterview, ...prev]);

      return newInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchInterviews();
  }, [filters?.caseId, filters?.status, filters?.limit, filters?.offset]);

  return {
    interviews,
    loading,
    error,
    refetch: fetchInterviews,
    createInterview,
  };
}

interface UseInterviewResult {
  interview: Interview | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  updateInterview: (updates: UpdateInterviewRequest) => Promise<Interview>;
  startInterview: () => Promise<Interview>;
  endInterview: () => Promise<Interview>;
  deleteInterview: () => Promise<void>;
}

export function useInterview(interviewId: string): UseInterviewResult {
  const [interview, setInterview] = useState<Interview | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchInterview = async () => {
    try {
      setLoading(true);
      setError(null);

      const interviewResult = await InterviewService.getInterviewById(interviewId);
      setInterview(interviewResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setInterview(null);
    } finally {
      setLoading(false);
    }
  };

  const updateInterview = async (updates: UpdateInterviewRequest): Promise<Interview> => {
    try {
      setError(null);

      const updatedInterview = await InterviewService.updateInterview(interviewId, updates);
      setInterview(updatedInterview);

      return updatedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const startInterview = async (): Promise<Interview> => {
    try {
      setError(null);

      const startedInterview = await InterviewService.startInterview(interviewId);
      setInterview(startedInterview);

      return startedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const endInterview = async (): Promise<Interview> => {
    try {
      setError(null);

      const endedInterview = await InterviewService.endInterview(interviewId);
      setInterview(endedInterview);

      return endedInterview;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteInterview = async (): Promise<void> => {
    try {
      setError(null);

      await InterviewService.deleteInterview(interviewId);
      setInterview(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    if (interviewId) {
      fetchInterview();
    }
  }, [interviewId]);

  return {
    interview,
    loading,
    error,
    refetch: fetchInterview,
    updateInterview,
    startInterview,
    endInterview,
    deleteInterview,
  };
}
