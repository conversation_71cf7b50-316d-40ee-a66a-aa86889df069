"use client"

import { useState, useEffect } from 'react';
import { CaseService } from '@/lib/supabase';
import type { Case, CaseFilters, CreateCaseRequest } from '@/types/database';

interface UseCasesResult {
  cases: Case[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  createCase: (caseData: CreateCaseRequest) => Promise<Case>;
}

export function useCases(filters?: CaseFilters): UseCasesResult {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCases = async () => {
    try {
      setLoading(true);
      setError(null);

      const cases = await CaseService.getCases(filters);
      setCases(cases);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setCases([]);
    } finally {
      setLoading(false);
    }
  };

  const createCase = async (caseData: CreateCaseRequest): Promise<Case> => {
    try {
      setError(null);

      const newCase = await CaseService.createCase(caseData);

      // Add the new case to the local state
      setCases(prev => [newCase, ...prev]);

      return newCase;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchCases();
  }, [filters?.status, filters?.officer, filters?.limit, filters?.offset]);

  // Refetch when the page becomes visible again (handles navigation back)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        fetchCases();
      }
    };

    const handleFocus = () => {
      fetchCases();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  return {
    cases,
    loading,
    error,
    refetch: fetchCases,
    createCase,
  };
}

interface UseCaseResult {
  case: Case | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useCase(caseId: string): UseCaseResult {
  const [caseData, setCaseData] = useState<Case | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCase = async () => {
    try {
      setLoading(true);
      setError(null);

      const caseResult = await CaseService.getCaseById(caseId);
      setCaseData(caseResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setCaseData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (caseId) {
      fetchCase();
    }
  }, [caseId]);

  // Refetch when the page becomes visible again (handles navigation back)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && caseId) {
        fetchCase();
      }
    };

    const handleFocus = () => {
      if (caseId) {
        fetchCase();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [caseId]);

  return {
    case: caseData,
    loading,
    error,
    refetch: fetchCase,
  };
}
