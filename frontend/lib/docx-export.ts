import {
  Document,
  Packer,
  Paragraph,
  TextRun,
  HeadingLevel,
  AlignmentType,
  BorderStyle,
  type Table,
  ShadingType,
  ImageRun,
} from "docx"
import { saveAs } from "file-saver"
import type { Interview, TranscriptData, Statement, Case, User } from "@/types/database"
import { INTERVIEW_ENVIRONMENT_LABELS } from "@/types/database"
import { convertMarkdownToDOCX } from "@/components/docx-markdown-parser"

interface DOCXDocumentProps {
  interview: Interview
  case: Case
  officer: User
  transcriptData?: TranscriptData
  statement?: Statement
}

const formatDate = (date: Date) => {
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  })
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit",
  })
}

const formatDuration = (seconds: number) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  if (hours > 0) {
    return `${hours}h ${minutes}m ${remainingSeconds}s`
  } else if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  } else {
    return `${remainingSeconds}s`
  }
}

const getEnvironmentLabel = (environment?: string) => {
  if (!environment) return "Not specified"
  return INTERVIEW_ENVIRONMENT_LABELS[environment as keyof typeof INTERVIEW_ENVIRONMENT_LABELS] || environment
}

// Helper function to create a section header
const createSectionHeader = (title: string) => {
  return new Paragraph({
    children: [
      new TextRun({
        text: title,
        bold: true,
        size: 28, // 14pt
      }),
    ],
    heading: HeadingLevel.HEADING_2,
    spacing: {
      before: 240, // 12pt
      after: 120, // 6pt
    },
    border: {
      bottom: {
        color: "CCCCCC",
        space: 1,
        style: BorderStyle.SINGLE,
        size: 6,
      },
    },
  })
}

// Helper function to create a data row
const createDataRow = (label: string, value: string) => {
  return new Paragraph({
    children: [
      new TextRun({
        text: `${label}: `,
        bold: true,
      }),
      new TextRun({
        text: value,
      }),
    ],
    spacing: {
      after: 80, // 4pt
    },
  })
}

export const generateInterviewDOCX = async ({
  interview,
  case: caseData,
  officer,
  transcriptData,
  statement,
}: DOCXDocumentProps): Promise<Blob> => {
  // Fetch signature image as base64 if it exists
  let interviewWithSignature = interview
  if (interview.witnessSignaturePath) {
    try {
      const response = await fetch(`/api/signature-base64?url=${encodeURIComponent(interview.witnessSignaturePath)}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.base64) {
          interviewWithSignature = {
            ...interview,
            witnessSignaturePath: data.base64
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch signature for DOCX:', error)
      // Continue without signature if fetch fails
    }
  }

  const children: (Paragraph | Table)[] = []

  // Header
  children.push(
    new Paragraph({
      children: [
        new TextRun({
          text: "FIRE INVESTIGATION UNIT",
          bold: true,
          size: 36, // 18pt
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 120,
      },
    }),
    new Paragraph({
      children: [
        new TextRun({
          text: "Witness Interview Report",
          size: 24, // 12pt
          color: "666666",
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        after: 240,
      },
      border: {
        bottom: {
          color: "000000",
          space: 1,
          style: BorderStyle.SINGLE,
          size: 6,
        },
      },
    }),
  )

  // Case Information Section
  children.push(createSectionHeader("Case Information"))
  children.push(createDataRow("Case ID", caseData.id))
  children.push(createDataRow("Incident Location", caseData.incidentLocation))
  children.push(createDataRow("Incident Date", `${caseData.incidentDate} at ${caseData.incidentTime}`))
  children.push(createDataRow("Case Status", caseData.status))
  children.push(createDataRow("Assigned Officer", caseData.assignedOfficer?.fullName || "Unknown"))

  // Interview Information Section
  children.push(createSectionHeader("Interview Information"))
  children.push(createDataRow("Interview ID", interview.id))
  children.push(createDataRow("Interviewing Officer", officer.fullName))
  children.push(createDataRow("Badge Number", officer.badgeNumber || "N/A"))
  children.push(createDataRow("Department", officer.department || "N/A"))
  children.push(
    createDataRow(
      "Interview Date",
      interview.startTime ? formatDate(interview.startTime) : formatDate(interview.createdAt),
    ),
  )
  children.push(createDataRow("Start Time", interview.startTime ? formatTime(interview.startTime) : "Not recorded"))
  children.push(createDataRow("End Time", interview.endTime ? formatTime(interview.endTime) : "Not recorded"))
  children.push(createDataRow("Duration", interview.duration ? formatDuration(interview.duration) : "Not recorded"))

  // Witness Information Section
  children.push(createSectionHeader("Witness Information"))
  children.push(createDataRow("Name", interview.witness.name))
  children.push(createDataRow("Type", interview.witness.type))
  children.push(createDataRow("Contact", interview.witness.contact))
  children.push(createDataRow("Environment", getEnvironmentLabel(interview.witness.environment)))

  // Statement Section - UPDATED WITH MARKDOWN FORMATTING
  if (statement) {
    children.push(createSectionHeader("Statement Summary"))

    // Convert markdown to DOCX paragraphs and add them
    const markdownParagraphs = convertMarkdownToDOCX(statement.content)
    children.push(...markdownParagraphs)
  }

  // Transcription Section
  // if (transcriptData && transcriptData.segments.length > 0) {
  //   children.push(createSectionHeader("Interview Transcript"));
  //   transcriptData.segments.forEach((segment, index) => {
  //     const speaker = transcriptData.speakers.find(s => s.id === segment.speaker);
  //     children.push(
  //       new Paragraph({
  //         children: [
  //           new TextRun({
  //             text: `${speaker?.name || 'Unknown'}: `,
  //             bold: true,
  //           }),
  //           new TextRun({
  //             text: segment.text,
  //           }),
  //           new TextRun({
  //             text: ` [${segment.timestamp}]`,
  //             size: 18, // 9pt
  //             color: "666666",
  //           }),
  //         ],
  //         spacing: {
  //           after: 120,
  //         },
  //       })
  //     );
  //   });
  // }

  // Officer Notes Section
  if (statement?.officerNotes) {
    children.push(createSectionHeader("Officer Notes"))
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: statement.officerNotes,
          }),
        ],
        spacing: {
          after: 120,
        },
        shading: {
          type: ShadingType.SOLID,
          color: "F0F0F0",
        },
      }),
    )
  }

  // Witness Signature Section
  if (interviewWithSignature.witnessSignaturePath) {
    children.push(createSectionHeader("Witness Signature"))

    // Add signature note
    children.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `Digital signature captured during interview on ${interviewWithSignature.startTime ? interviewWithSignature.startTime.toLocaleDateString("en-US") : interviewWithSignature.createdAt.toLocaleDateString("en-US")}`,
            size: 18, // 9pt
            color: "666666",
            italics: true,
          }),
        ],
        alignment: AlignmentType.CENTER,
        spacing: {
          after: 120,
        },
      }),
    )

    // Try to embed the signature image if it's base64
    if (interviewWithSignature.witnessSignaturePath.startsWith('data:image/')) {
      try {
        // Extract base64 data
        const base64Data = interviewWithSignature.witnessSignaturePath.split(',')[1]
        const imageBuffer = Buffer.from(base64Data, 'base64')

        children.push(
          new Paragraph({
            children: [
              new ImageRun({
                data: imageBuffer,
                transformation: {
                  width: 300,
                  height: 150,
                },
                type: "png",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 240,
            },
          }),
        )
      } catch (error) {
        console.error('Failed to embed signature image in DOCX:', error)
        // Fallback to text
        children.push(
          new Paragraph({
            children: [
              new TextRun({
                text: "Signature image could not be embedded",
                size: 16, // 8pt
                color: "666666",
              }),
            ],
            alignment: AlignmentType.CENTER,
            spacing: {
              after: 240,
            },
          }),
        )
      }
    } else {
      // Fallback for non-base64 URLs
      children.push(
        new Paragraph({
          children: [
            new TextRun({
              text: `Signature Image URL: ${interviewWithSignature.witnessSignaturePath}`,
              size: 16, // 8pt
              color: "666666",
            }),
          ],
          alignment: AlignmentType.CENTER,
          spacing: {
            after: 240,
          },
          shading: {
            type: ShadingType.SOLID,
            color: "F9F9F9",
          },
        }),
      )
    }
  }

  // Footer
  children.push(
    new Paragraph({
      children: [
        new TextRun({
          text: `Generated on ${new Date().toLocaleDateString("en-US")} at ${new Date().toLocaleTimeString("en-US")} | Fire Investigation Unit - Witness Interview System`,
          size: 18, // 9pt
          color: "666666",
        }),
      ],
      alignment: AlignmentType.CENTER,
      spacing: {
        before: 480, // 24pt
      },
      border: {
        top: {
          color: "CCCCCC",
          space: 1,
          style: BorderStyle.SINGLE,
          size: 6,
        },
      },
    }),
  )

  const doc = new Document({
    sections: [
      {
        properties: {},
        children: children,
      },
    ],
  })

  return await Packer.toBlob(doc)
}

export const downloadDOCX = (blob: Blob, filename: string) => {
  saveAs(blob, filename)
}
