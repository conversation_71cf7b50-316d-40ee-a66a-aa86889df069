import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY!
const assetBucketName = process.env.ASSET_BUCKET_NAME!

// Create a Supabase client with service key for private bucket access
const supabase = createClient(supabaseUrl, supabaseServiceKey)

/**
 * Fetch an asset from Supabase bucket and convert to base64 for embedding in exports
 */
export const fetchAssetAsBase64 = async (fileName: string): Promise<string | null> => {
  try {
    console.log(`Fetching asset: ${fileName} from bucket: ${assetBucketName}`)
    
    // Download the file from Supabase storage
    const { data, error } = await supabase.storage
      .from(assetBucketName)
      .download(fileName)

    if (error) {
      console.error('Error downloading asset:', error)
      return null
    }

    if (!data) {
      console.error('No data received for asset:', fileName)
      return null
    }

    // Convert blob to array buffer
    const arrayBuffer = await data.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    // Convert to base64
    const base64 = buffer.toString('base64')
    const mimeType = data.type || 'image/png'
    
    console.log(`Successfully loaded asset: ${fileName}, size: ${buffer.length} bytes`)
    return `data:${mimeType};base64,${base64}`
  } catch (error) {
    console.error('Error fetching asset as base64:', error)
    return null
  }
}

/**
 * Fetch an asset from Supabase bucket and return as Buffer for DOCX embedding
 */
export const fetchAssetAsBuffer = async (fileName: string): Promise<Buffer | null> => {
  try {
    console.log(`Fetching asset as buffer: ${fileName} from bucket: ${assetBucketName}`)
    
    // Download the file from Supabase storage
    const { data, error } = await supabase.storage
      .from(assetBucketName)
      .download(fileName)

    if (error) {
      console.error('Error downloading asset:', error)
      return null
    }

    if (!data) {
      console.error('No data received for asset:', fileName)
      return null
    }

    // Convert blob to array buffer then to Buffer
    const arrayBuffer = await data.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    
    console.log(`Successfully loaded asset buffer: ${fileName}, size: ${buffer.length} bytes`)
    return buffer
  } catch (error) {
    console.error('Error fetching asset as buffer:', error)
    return null
  }
}

/**
 * Get a signed URL for an asset (for public access)
 */
export const getAssetSignedUrl = async (fileName: string, expiresIn: number = 3600): Promise<string | null> => {
  try {
    const { data, error } = await supabase.storage
      .from(assetBucketName)
      .createSignedUrl(fileName, expiresIn)

    if (error) {
      console.error('Error creating signed URL for asset:', error)
      return null
    }

    return data.signedUrl
  } catch (error) {
    console.error('Error getting asset signed URL:', error)
    return null
  }
}

/**
 * Check if an asset exists in the bucket
 */
export const assetExists = async (fileName: string): Promise<boolean> => {
  try {
    const { data, error } = await supabase.storage
      .from(assetBucketName)
      .list('', {
        search: fileName
      })

    if (error) {
      console.error('Error checking asset existence:', error)
      return false
    }

    return data.some(file => file.name === fileName)
  } catch (error) {
    console.error('Error checking asset existence:', error)
    return false
  }
}
