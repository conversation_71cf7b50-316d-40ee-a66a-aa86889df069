import type React from "react"
import { Document, Page, Text, View, StyleSheet, pdf, Image } from "@react-pdf/renderer"
import type { Interview, TranscriptData, Statement, Case, User, StatementFormData } from "@/types/database"
import { INTERVIEW_ENVIRONMENT_LABELS } from "@/types/database"
import { PDFMarkdown } from "@/components/pdf_markdown_parser"
import { fetchStatementFormSignatures, type StatementFormSignatures } from "@/lib/statement-signature-utils"

// PDF Styles (keeping your existing styles)
const styles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#FFFFFF",
    padding: 30,
    fontSize: 11,
    lineHeight: 1.4,
  },
  header: {
    marginBottom: 20,
    borderBottom: 1,
    borderBottomColor: "#000000",
    paddingBottom: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 5,
  },
  subtitle: {
    fontSize: 12,
    textAlign: "center",
    color: "#666666",
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
    color: "#000000",
    borderBottom: 1,
    borderBottomColor: "#CCCCCC",
    paddingBottom: 2,
  },
  row: {
    flexDirection: "row",
    marginBottom: 4,
  },
  label: {
    width: "30%",
    fontWeight: "bold",
    color: "#333333",
  },
  value: {
    width: "70%",
    color: "#000000",
  },
  transcriptContainer: {
    marginTop: 10,
  },
  transcriptSegment: {
    marginBottom: 8,
    flexDirection: "row",
  },
  speaker: {
    width: "20%",
    fontWeight: "bold",
    color: "#000000",
    paddingRight: 10,
  },
  timestamp: {
    fontSize: 9,
    color: "#666666",
    marginTop: 2,
  },
  transcriptText: {
    width: "80%",
    color: "#000000",
    lineHeight: 1.3,
  },
  statement: {
    backgroundColor: "#F8F9FA",
    padding: 10,
    marginTop: 5,
    lineHeight: 1.4,
  },
  officerNotes: {
    backgroundColor: "#F0F0F0",
    padding: 8,
    marginTop: 10,
    fontSize: 10,
    lineHeight: 1.3,
  },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: "center",
    fontSize: 9,
    color: "#666666",
    borderTop: 1,
    borderTopColor: "#CCCCCC",
    paddingTop: 10,
  },
  signatureContainer: {
    alignItems: "center",
    padding: 10,
    borderTop: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderRight: 1,
    borderColor: "#CCCCCC",
    backgroundColor: "#FAFAFA",
  },
  signatureImage: {
    maxWidth: 300,
    maxHeight: 100,
    marginBottom: 8,
  },
  signatureNote: {
    fontSize: 8,
    color: "#666666",
    textAlign: "center",
    fontStyle: "italic",
  },
})

// Statement Form PDF Styles
const statementFormStyles = StyleSheet.create({
  page: {
    fontFamily: "Helvetica",
    fontSize: 9,
    padding: 25,
    lineHeight: 1.3,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
    textDecoration: "underline",
  },
  table: {
    width: "100%",
    borderStyle: "solid",
    borderWidth: 1,
    borderColor: "#000",
    marginBottom: 10,
  },
  tableRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#000",
    borderBottomStyle: "solid",
  },
  tableCell: {
    flex: 1,
    padding: 3,
    borderRightWidth: 1,
    borderRightColor: "#000",
    borderRightStyle: "solid",
    fontSize: 8,
    wordWrap: "break-word",
    overflow: "hidden",
  },
  tableCellLast: {
    flex: 1,
    padding: 3,
    fontSize: 8,
    wordWrap: "break-word",
    overflow: "hidden",
  },
  tableCellHeader: {
    flex: 1,
    padding: 4,
    borderRightWidth: 1,
    borderRightColor: "#000",
    borderRightStyle: "solid",
    fontSize: 9,
    fontWeight: "bold",
    backgroundColor: "#f0f0f0",
  },
  tableCellHeaderLast: {
    flex: 1,
    padding: 4,
    fontSize: 9,
    fontWeight: "bold",
    backgroundColor: "#f0f0f0",
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "bold",
    marginTop: 15,
    marginBottom: 10,
    backgroundColor: "#e0e0e0",
    padding: 5,
    textAlign: "center",
  },
  signatureSection: {
    marginTop: 20,
    padding: 10,
    borderTop: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderRight: 1,
    borderColor: "#000",
  },
  signatureRow: {
    flexDirection: "row",
    marginBottom: 10,
  },
  signatureColumn: {
    flex: 1,
    alignItems: "center",
    padding: 10,
  },
  signatureImage: {
    maxWidth: 150,
    maxHeight: 60,
    marginBottom: 10,
  },
  signatureLabel: {
    fontSize: 8,
    textAlign: "center",
    fontWeight: "bold",
  },
  signatureName: {
    fontSize: 8,
    textAlign: "center",
    marginTop: 2,
  },
  note: {
    fontSize: 8,
    fontStyle: "italic",
    marginTop: 10,
    textAlign: "left",
  },
  declaration: {
    marginTop: 15,
    padding: 10,
    borderTop: 1,
    borderBottom: 1,
    borderLeft: 1,
    borderRight: 1,
    borderColor: "#000",
    fontSize: 9,
  },
  declarationSignatures: {
    flexDirection: "row",
    marginTop: 20,
    justifyContent: "space-around",
    gap: 20,
  },
  declarationSignature: {
    flex: 1,
    alignItems: "center",
    padding: 10,
    maxWidth: "45%",
  },
})

interface PDFDocumentProps {
  interview: Interview
  case: Case
  officer: User
  transcriptData?: TranscriptData
  statement?: Statement
}

const InterviewPDFDocument: React.FC<PDFDocumentProps> = ({
  interview,
  case: caseData,
  officer,
  transcriptData,
  statement,
}) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const remainingSeconds = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${remainingSeconds}s`
    } else if (minutes > 0) {
      return `${minutes}m ${remainingSeconds}s`
    } else {
      return `${remainingSeconds}s`
    }
  }

  const getEnvironmentLabel = (environment?: string) => {
    if (!environment) return "Not specified"
    return INTERVIEW_ENVIRONMENT_LABELS[environment as keyof typeof INTERVIEW_ENVIRONMENT_LABELS] || environment
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>FIRE INVESTIGATION UNIT</Text>
          <Text style={styles.subtitle}>Witness Interview Report</Text>
        </View>

        {/* Case Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Case Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Case ID:</Text>
            <Text style={styles.value}>{caseData.id}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Incident Location:</Text>
            <Text style={styles.value}>{caseData.incidentLocation}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Incident Date:</Text>
            <Text style={styles.value}>
              {caseData.incidentDate} at {caseData.incidentTime}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Case Status:</Text>
            <Text style={styles.value}>{caseData.status}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Assigned Officer:</Text>
            <Text style={styles.value}>{caseData.assignedOfficer?.fullName || "Unknown"}</Text>
          </View>
        </View>

        {/* Interview Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Interview Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Interview ID:</Text>
            <Text style={styles.value}>{interview.id}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Interviewing Officer:</Text>
            <Text style={styles.value}>{officer.fullName}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Badge Number:</Text>
            <Text style={styles.value}>{officer.badgeNumber || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Department:</Text>
            <Text style={styles.value}>{officer.department || "N/A"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Interview Date:</Text>
            <Text style={styles.value}>
              {interview.startTime ? formatDate(interview.startTime) : formatDate(interview.createdAt)}
            </Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Start Time:</Text>
            <Text style={styles.value}>{interview.startTime ? formatTime(interview.startTime) : "Not recorded"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>End Time:</Text>
            <Text style={styles.value}>{interview.endTime ? formatTime(interview.endTime) : "Not recorded"}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Duration:</Text>
            <Text style={styles.value}>{interview.duration ? formatDuration(interview.duration) : "Not recorded"}</Text>
          </View>
        </View>

        {/* Witness Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Witness Information</Text>
          <View style={styles.row}>
            <Text style={styles.label}>Name:</Text>
            <Text style={styles.value}>{interview.witness.name}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Type:</Text>
            <Text style={styles.value}>{interview.witness.type}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Contact:</Text>
            <Text style={styles.value}>{interview.witness.contact}</Text>
          </View>
          <View style={styles.row}>
            <Text style={styles.label}>Environment:</Text>
            <Text style={styles.value}>{getEnvironmentLabel(interview.witness.environment)}</Text>
          </View>
        </View>

        {/* Statement - NOW WITH MARKDOWN FORMATTING! */}
        {statement && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Statement Summary</Text>
            <View style={styles.statement}>
              <PDFMarkdown content={statement.content} />
            </View>
          </View>
        )}

        {/* Officer Notes */}
        {statement?.officerNotes && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Officer Notes</Text>
            <View style={styles.officerNotes}>
              <Text>{statement.officerNotes}</Text>
            </View>
          </View>
        )}

        {/* Witness Signature */}
        {interview.witnessSignaturePath && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Witness Signature</Text>
            <View style={styles.signatureContainer}>
              <Image
                src={interview.witnessSignaturePath}
                style={styles.signatureImage}
              />
              <Text style={styles.signatureNote}>
                Digital signature captured during interview on {interview.startTime ? formatDate(interview.startTime) : formatDate(interview.createdAt)}
              </Text>
            </View>
          </View>
        )}

        {/* Footer */}
        <Text style={styles.footer}>
          Generated on {new Date().toLocaleDateString("en-US")} at {new Date().toLocaleTimeString("en-US")} | Fire
          Investigation Unit - Witness Interview System
        </Text>
      </Page>
    </Document>
  )
}

// Statement Form PDF Component
interface StatementFormPDFProps {
  statementForm: StatementFormData
  signatures: StatementFormSignatures
}

const StatementFormPDFDocument: React.FC<StatementFormPDFProps> = ({
  statementForm,
  signatures,
}) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      })
    } catch {
      return dateString
    }
  }

  const formatTime = (timeString: string) => {
    if (!timeString) return ""
    return timeString
  }

  const formatIdentificationType = (selectedType: string) => {
    const types = ['NRIC', 'FIN', 'Passport', 'Work Permit', 'Employment Pass']
    return types.map((type, index) => {
      const isSelected = type === selectedType
      const separator = index < types.length - 1 ? ' / ' : ''
      return (
        <Text key={type} style={{
          fontSize: 8,
          textDecoration: isSelected ? 'none' : 'line-through',
          color: isSelected ? '#000' : '#666'
        }}>
          {type}{separator}
        </Text>
      )
    })
  }

  return (
    <Document>
      <Page size="A4" style={statementFormStyles.page}>
        {/* Title */}
        <Text style={statementFormStyles.title}>STATEMENT FORM</Text>

        {/* Personal Information Table */}
        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>
              Name (including aliases, if any) of person making statement
            </Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>
              Name (including aliases, if any) and Identification number of Guardian
            </Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.personName || ""}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.guardianName || ""}
            </Text>
          </View>
        </View>

        {/* Incident and Personal Details Table */}
        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>SCDF Incident No.</Text>
            <Text style={statementFormStyles.tableCellHeader}>Date of Incident</Text>
            <Text style={statementFormStyles.tableCellHeader}>Gender</Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>Age</Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.scdfIncidentNo || ""}
            </Text>
            <Text style={statementFormStyles.tableCell}>
              {formatDate(statementForm.incidentDate)}
            </Text>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.gender || ""}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.age || ""}
            </Text>
          </View>
        </View>

        {/* Additional Personal Details */}
        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>Date of Birth</Text>
            <Text style={statementFormStyles.tableCellHeader}>Marital Status</Text>
            <View style={statementFormStyles.tableCellHeader}>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                <Text style={{ fontSize: 8, fontWeight: "bold" }}>* </Text>
                {formatIdentificationType(statementForm.identificationType)}
                <Text style={{ fontSize: 8, fontWeight: "bold" }}> Number</Text>
              </View>
            </View>
            <Text style={statementFormStyles.tableCellHeaderLast}>Nationality</Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {formatDate(statementForm.dateOfBirth)}
            </Text>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.maritalStatus || ""}
            </Text>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.identificationNumber || ""}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.nationality || ""}
            </Text>
          </View>
        </View>

        {/* Address and Contact Information */}
        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>Residential Address</Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>Occupation</Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.residentialAddress || ""}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.occupation || ""}
            </Text>
          </View>
        </View>

        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>Employment Address</Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>
              Telephone Numbers Contact: Guardian Telephone Number
            </Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.employmentAddress || ""}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.telephoneNumber || ""} / {statementForm.guardianTelephoneNumber || ""}
            </Text>
          </View>
        </View>

        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeaderLast}>Email Address</Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.emailAddress || ""}
            </Text>
          </View>
        </View>

        {/* Statement Recorded Section */}
        <Text style={statementFormStyles.sectionTitle}>Statement Recorded</Text>

        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>at (time)</Text>
            <Text style={statementFormStyles.tableCellHeader}>on (date)</Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>at (place)</Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              Start time: {formatTime(statementForm.recordingStartTime)}
              {"\n"}End time: {formatTime(statementForm.recordingEndTime)}
            </Text>
            <Text style={statementFormStyles.tableCell}>
              {formatDate(statementForm.recordingDate)}
            </Text>
            <Text style={statementFormStyles.tableCellLast}>
              {statementForm.recordingPlace || ""}
            </Text>
          </View>
        </View>

        <View style={statementFormStyles.table}>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCellHeader}>Language Spoken</Text>
            <Text style={statementFormStyles.tableCellHeader}>
              Interpreted by (if applicable, signature here)
            </Text>
            <Text style={statementFormStyles.tableCellHeaderLast}>
              Recorded by (signature here)
            </Text>
          </View>
          <View style={statementFormStyles.tableRow}>
            <Text style={statementFormStyles.tableCell}>
              {statementForm.languageSpoken || ""}
            </Text>
            <View style={statementFormStyles.tableCell}>
              {signatures.interpreterSignature && (
                <Image
                  src={signatures.interpreterSignature}
                  style={statementFormStyles.signatureImage}
                />
              )}
              <Text style={statementFormStyles.signatureName}>
                Name: {statementForm.interpreterName || ""}
                {"\n"}Contact: {statementForm.interpreterContact || ""}
              </Text>
            </View>
            <View style={statementFormStyles.tableCellLast}>
              {signatures.recordingOfficerSignature && (
                <Image
                  src={signatures.recordingOfficerSignature}
                  style={statementFormStyles.signatureImage}
                />
              )}
              <Text style={statementFormStyles.signatureName}>
                Name: {statementForm.recordingOfficerName || ""}
              </Text>
            </View>
          </View>
        </View>

        {/* Notes */}
        <Text style={statementFormStyles.note}>
          NOTE:{"\n"}
          The statement is to be signed and dated by the person making it and by the Recorder and the Interpreter, if one is used.{"\n"}
          * Delete as necessary
        </Text>

        {/* Declaration */}
        <View style={statementFormStyles.declaration} wrap={false}>
          <Text>I hereby declare that the details provided in the above are true.</Text>

          <View style={statementFormStyles.declarationSignatures}>
            <View style={statementFormStyles.declarationSignature}>
              {signatures.statementMakerSignature && (
                <Image
                  src={signatures.statementMakerSignature}
                  style={statementFormStyles.signatureImage}
                />
              )}
              <Text style={statementFormStyles.signatureLabel}>
                (Name & Signature of person making statement)
              </Text>
              <Text style={statementFormStyles.signatureName}>
                {statementForm.statementMakerName || ""}
              </Text>
            </View>

            <View style={statementFormStyles.declarationSignature}>
              {signatures.scdfInterviewerSignature && (
                <Image
                  src={signatures.scdfInterviewerSignature}
                  style={statementFormStyles.signatureImage}
                />
              )}
              <Text style={statementFormStyles.signatureLabel}>
                (Name & Signature of SCDF interviewer)
              </Text>
              <Text style={statementFormStyles.signatureName}>
                {statementForm.scdfInterviewerName || ""}
              </Text>
            </View>
          </View>
        </View>
      </Page>
    </Document>
  )
}

export const generateInterviewPDF = async (
  interview: Interview,
  caseData: Case,
  officer: User,
  transcriptData?: TranscriptData,
  statement?: Statement,
): Promise<Blob> => {
  // Fetch signature image as base64 if it exists
  let interviewWithSignature = interview
  if (interview.witnessSignaturePath) {
    try {
      const response = await fetch(`/api/signature-base64?url=${encodeURIComponent(interview.witnessSignaturePath)}`)
      if (response.ok) {
        const data = await response.json()
        if (data.success && data.base64) {
          interviewWithSignature = {
            ...interview,
            witnessSignaturePath: data.base64
          }
        }
      }
    } catch (error) {
      console.error('Failed to fetch signature for PDF:', error)
      // Continue without signature if fetch fails
    }
  }

  const doc = (
    <InterviewPDFDocument
      interview={interviewWithSignature}
      case={caseData}
      officer={officer}
      transcriptData={transcriptData}
      statement={statement}
    />
  )

  return await pdf(doc).toBlob()
}

export const generateStatementFormPDF = async (
  statementForm: StatementFormData
): Promise<Blob> => {
  // Fetch all statement form signatures as base64
  const signatures = await fetchStatementFormSignatures(statementForm)

  const doc = (
    <StatementFormPDFDocument
      statementForm={statementForm}
      signatures={signatures}
    />
  )

  return await pdf(doc).toBlob()
}

export const downloadPDF = (blob: Blob, filename: string) => {
  const url = URL.createObjectURL(blob)

  // Download the file
  const link = document.createElement("a")
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  // Open in new tab
  window.open(url, "_blank")

  // Clean up
  setTimeout(() => URL.revokeObjectURL(url), 100)
}
