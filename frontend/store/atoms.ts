import { atom } from 'jotai'
import type { Case, Witness, Interview, User, Statement, TranscriptData, StatementInfo } from '@/types/database'

// Screen navigation atom
export const currentScreenAtom = atom<
  | "start-screen"
  | "case-selection"
  | "new-case"
  | "witness-setup"
  | "recording-screen"
  | "transcription-screen"
  | "statement-edit"
  | "export-screen"
  | "case-history"
  | "case-details"
  | "interview-details"
  | "loading-screen"
>("start-screen")

// Case-related atoms
export const currentCaseAtom = atom<Case | null>(null)
export const selectedCaseAtom = atom<Case | null>(null)

// Witness-related atoms
export const currentWitnessAtom = atom<Witness | null>(null)

// Interview-related atoms
export const currentInterviewAtom = atom<Interview | null>(null)
export const selectedInterviewAtom = atom<Interview | null>(null)

// Recording state atoms
export const isRecordingAtom = atom<boolean>(false)
export const isPausedAtom = atom<boolean>(false)
export const recordingStartTimeAtom = atom<number | null>(null)
export const recordingEndTimeAtom = atom<number | null>(null)
export const saveRecordingAtom = atom<boolean>(true) // Default to saving recordings
export const recordingUrlAtom = atom<string | null>(null) // Store recording URL from websocket

// Transcription and content atoms
export const transcriptDataAtom = atom<TranscriptData | null>(null)
export const summaryDataAtom = atom<string | null>(null)
export const officerNotesAtom = atom<string | null>(null)

// Statement info from websocket
export const statementInfoAtom = atom<StatementInfo | null>(null)

// Export data atom
export const exportDataAtom = atom<{
  interview: Interview | null
  case: Case | null
  officer: User | null
  transcriptData: TranscriptData | null
  statement: Statement | null
} | null>(null)

// Derived atoms for common combinations
export const recordingStateAtom = atom(
  (get) => ({
    isRecording: get(isRecordingAtom),
    isPaused: get(isPausedAtom),
    startTime: get(recordingStartTimeAtom),
    endTime: get(recordingEndTimeAtom),
    saveRecording: get(saveRecordingAtom),
    recordingUrl: get(recordingUrlAtom),
  })
)

export const interviewDataAtom = atom(
  (get) => ({
    currentCase: get(currentCaseAtom),
    currentWitness: get(currentWitnessAtom),
    currentInterview: get(currentInterviewAtom),
    transcriptData: get(transcriptDataAtom),
    summaryData: get(summaryDataAtom),
    officerNotes: get(officerNotesAtom),
  })
)

// Reset functions as write-only atoms
export const resetCaseDataAtom = atom(
  null,
  (get, set) => {
    set(currentCaseAtom, null)
    set(selectedCaseAtom, null)
  }
)

export const resetWitnessDataAtom = atom(
  null,
  (get, set) => {
    set(currentWitnessAtom, null)
  }
)

export const resetInterviewDataAtom = atom(
  null,
  (get, set) => {
    set(currentInterviewAtom, null)
    set(selectedInterviewAtom, null)
  }
)

export const resetRecordingDataAtom = atom(
  null,
  (get, set) => {
    set(isRecordingAtom, false)
    set(isPausedAtom, false)
    set(recordingStartTimeAtom, null)
    set(recordingEndTimeAtom, null)
    set(saveRecordingAtom, true) // Reset to default (save recordings)
    set(recordingUrlAtom, null) // Reset recording URL
  }
)

export const resetTranscriptionDataAtom = atom(
  null,
  (get, set) => {
    set(transcriptDataAtom, null)
    set(summaryDataAtom, null)
    set(officerNotesAtom, null)
  }
)

export const resetExportDataAtom = atom(
  null,
  (get, set) => {
    set(exportDataAtom, null)
  }
)

// Complete reset atom
export const resetAllDataAtom = atom(
  null,
  (get, set) => {
    set(resetCaseDataAtom, null)
    set(resetWitnessDataAtom, null)
    set(resetInterviewDataAtom, null)
    set(resetRecordingDataAtom, null)
    set(resetTranscriptionDataAtom, null)
    set(resetExportDataAtom, null)
  }
)
